<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import { createBaiduASR, checkRecordingSupport, type BaiduASRManager } from '@/utils/baiduASR'
import { createAudioProcessor, type AudioProcessor } from '@/utils/audioProcessor'
import { getBaiduASRConfig, validateConfig, printConfigHelp } from '@/config/baiduASR'

// Emits定义
const emit = defineEmits<{
  start: []
  end: []
  result: [text: string]
  error: [error: string]
}>()

// 响应式数据
const isRecording = ref(false)
const isSupported = ref(false)
const asrManager = ref<BaiduASRManager | null>(null)
const audioProcessor = ref<AudioProcessor | null>(null)

// 获取百度ASR配置
const ASR_CONFIG = getBaiduASRConfig()

// 检查支持性
const checkSupport = () => {
  const support = checkRecordingSupport()
  isSupported.value = support.supported

  if (!support.supported) {
    console.error('录音功能不支持:', support.message)
    return false
  }

  // 验证百度ASR配置
  if (!validateConfig(ASR_CONFIG)) {
    console.error('百度ASR配置无效')
    printConfigHelp()
    isSupported.value = false
    return false
  }

  return true
}

// 初始化
const init = () => {
  try {
    // 创建百度ASR管理器，传入回调函数
    asrManager.value = createBaiduASR(ASR_CONFIG, {
      onStart: () => emit('start'),
      onEnd: () => emit('end'),
      onResult: (text: string) => emit('result', text),
      onError: (error: string) => emit('error', error)
    })

    // 创建音频处理器
    audioProcessor.value = createAudioProcessor()

    console.log('百度ASR录音器初始化成功')
  } catch (error) {
    console.error('初始化失败:', error)
    emit('error', '初始化失败')
  }
}

// 开始录音
const startRecording = async () => {
  if (!isSupported.value || !asrManager.value) {
    emit('error', '设备不支持录音功能')
    return
  }

  if (isRecording.value) {
    console.warn('正在录音中')
    return
  }

  try {
    await asrManager.value.startRecording()
    isRecording.value = true
    console.log('开始录音')
  } catch (error) {
    console.error('开始录音失败:', error)
    emit('error', error instanceof Error ? error.message : '开始录音失败')
  }
}

// 停止录音
const stopRecording = () => {
  if (!asrManager.value || !isRecording.value) {
    return
  }

  try {
    asrManager.value.stopRecording()
    isRecording.value = false
    console.log('停止录音')
  } catch (error) {
    console.error('停止录音失败:', error)
    emit('error', '停止录音失败')
  }
}

// 切换录音状态
const toggleRecording = () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}

// 长按开始录音
const handleTouchStart = (event: TouchEvent) => {
  event.preventDefault()
  if (!isRecording.value) {
    startRecording()
  }
}

// 长按结束停止录音
const handleTouchEnd = (event: TouchEvent) => {
  event.preventDefault()
  if (isRecording.value) {
    stopRecording()
  }
}

// 生命周期
onMounted(() => {
  checkSupport()
  if (isSupported.value) {
    init()
  }
})

onUnmounted(() => {
  if (asrManager.value) {
    asrManager.value.destroy()
  }
  if (audioProcessor.value) {
    audioProcessor.value.destroy()
  }
})
</script>

<template>
  <div class="baidu-asr-recorder">
    <!-- 录音按钮 -->
    <div 
      class="record-button"
      :class="{ 
        'recording': isRecording,
        'disabled': !isSupported
      }"
      @click="toggleRecording"
      @touchstart="handleTouchStart"
      @touchend="handleTouchEnd"
    >
      <div class="button-inner">
        <van-icon 
          :name="isRecording ? 'pause' : 'volume-o'" 
          size="28" 
          color="white"
        />
      </div>
      
      <!-- 录音波纹动画 -->
      <div v-if="isRecording" class="recording-waves">
        <div class="wave wave-1"></div>
        <div class="wave wave-2"></div>
        <div class="wave wave-3"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.baidu-asr-recorder {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 录音按钮 */
.record-button {
  position: relative;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.record-button:active {
  transform: scale(0.95);
}

.record-button.recording {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4);
  animation: recordingPulse 1.5s infinite;
}

.record-button.disabled {
  background: #c8c9cc;
  cursor: not-allowed;
  box-shadow: none;
}

.record-button.disabled:active {
  transform: none;
}

.button-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  position: relative;
}

/* 录音波纹动画 */
.recording-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(255, 107, 107, 0.6);
  border-radius: 50%;
  animation: waveAnimation 2s infinite;
}

.wave-1 {
  width: 90px;
  height: 90px;
  animation-delay: 0s;
}

.wave-2 {
  width: 110px;
  height: 110px;
  animation-delay: 0.5s;
}

.wave-3 {
  width: 130px;
  height: 130px;
  animation-delay: 1s;
}

/* 动画效果 */
@keyframes recordingPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes waveAnimation {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* 移动端优化 */
@media (max-width: 767px) {
  .record-button {
    width: 60px;
    height: 60px;
  }
  
  .button-inner :deep(.van-icon) {
    font-size: 24px;
  }
  
  .wave-1 {
    width: 80px;
    height: 80px;
  }
  
  .wave-2 {
    width: 100px;
    height: 100px;
  }
  
  .wave-3 {
    width: 120px;
    height: 120px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .record-button:hover {
    transform: none;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .record-button,
  .wave {
    animation: none;
  }
  
  .record-button:active {
    transform: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .record-button {
    border: 2px solid #fff;
  }
}
</style>
