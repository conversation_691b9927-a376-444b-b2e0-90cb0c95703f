<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import RTMPPlayer from '@/components/RTMPPlayer.vue'
import BaiduASRRecorder from './components/BaiduASRRecorder.vue'

// 声明全局BRTC类型
declare global {
  interface Window {
    BRTC: {
      VERSION: string
      Start: (server: string, appid: string, token: string) => Promise<any>
      createClient: (config?: any) => any
      getDevices: () => Promise<any[]>
      getCameras: () => Promise<any[]>
      getMicrophones: () => Promise<any[]>
      getSpeakers: () => Promise<any[]>
      LOG_LEVEL: {
        DEBUG: number
        INFO: number
        WARNING: number
        ERROR: number
        NONE: number
      }
      setLogLevel: (level: number) => void
      checkSystemRequirements: () => any
      [key: string]: any
    }
  }
}

// 响应式数据
const isRecording = ref(false)
const recognizedText = ref('')
const isVideoConnected = ref(false)
const videoError = ref('')
const rtmpUrl = ref('rtmp://ns8.indexforce.com/home/<USER>')

// BRTC SDK 相关状态
const brtcInitialized = ref(false)
const brtcError = ref('')
const sdkVersion = ref('')

// BRTC 配置
const brtcConfig = {
  server: 'wss://rtc.baidubce.com',  // 百度RTC服务器
  appid: 'apppc7fpsryj72g',          // 百度派发的AppID
  token: 'test-token-123456'         // app server派发的token
}

// 视频连接处理
const handleVideoConnected = () => {
  isVideoConnected.value = true
  console.log('✅ 视频连接成功')
}

const handleVideoPlay = () => {
  console.log('▶️ 视频开始播放')
}

const handleVideoError = (error: string) => {
  videoError.value = error
  isVideoConnected.value = false
  console.error('❌ 视频错误:', error)
}

// 语音识别处理
const handleRecordingStart = () => {
  isRecording.value = true
  console.log('🎤 开始录音')
}

const handleRecordingStop = () => {
  isRecording.value = false
  console.log('⏹️ 停止录音')
}

const handleRecognitionResult = (text: string) => {
  recognizedText.value = text
  console.log('🗣️ 识别结果:', text)
  
  if (text.trim()) {
    showToast(`识别到: ${text}`)
  }
}

const handleRecognitionError = (error: string) => {
  console.error('❌ 语音识别错误:', error)
  showToast(`识别错误: ${error}`)
}

// 清除识别结果
const clearRecognizedText = () => {
  recognizedText.value = ''
}

/**
 * BRTC_Start() - 启动BRTC SDK
 * 根据百度RTC官方文档实现初始化功能
 *
 * @param server - 百度的RTC服务器，使用默认值即可
 * @param appid - 百度派发的AppID，开发者的唯一标识
 * @param token - app server派发的token字符串，用来校验通信的合法性
 */
const BRTC_Start = async (server?: string, appid?: string, token?: string) => {
  try {
    console.log('🚀 开始初始化BRTC SDK...')

    // 检查SDK是否可用
    if (!window.BRTC) {
      throw new Error('BRTC SDK未加载，请检查SDK引入')
    }

    // 使用传入的参数或默认配置
    const finalConfig = {
      server: server || brtcConfig.server,
      appid: appid || brtcConfig.appid,
      token: token || brtcConfig.token
    }

    // 验证必要参数
    if (!finalConfig.appid) {
      throw new Error('AppID不能为空，请先配置AppID')
    }

    if (!finalConfig.token) {
      throw new Error('Token不能为空，请先配置Token')
    }

    console.log(`使用配置: Server=${finalConfig.server}, AppID=${finalConfig.appid}`)

    // 调用百度RTC SDK的Start方法
    console.log('🚀 调用 BRTC.Start() 方法...')

    const result = await window.BRTC.Start(
      finalConfig.server,
      finalConfig.appid,
      finalConfig.token
    )

    console.log('✅ BRTC.Start() 调用成功')
    console.log('返回结果:', result)

    // 更新状态
    brtcInitialized.value = true
    brtcError.value = ''
    sdkVersion.value = window.BRTC.VERSION || 'Unknown'

    console.log('🎉 BRTC SDK初始化完成！')
    showToast('BRTC SDK初始化成功')

    return result

  } catch (error: any) {
    const errorMsg = error.message || '初始化失败'
    brtcError.value = errorMsg
    brtcInitialized.value = false

    console.error('❌ BRTC SDK初始化失败:', errorMsg)
    showToast(`初始化失败: ${errorMsg}`)

    throw error
  }
}

// 检查SDK加载状态
const checkSDKLoaded = () => {
  return new Promise<boolean>((resolve) => {
    let attempts = 0
    const maxAttempts = 50 // 最多等待5秒

    const checkInterval = setInterval(() => {
      attempts++

      if (window.BRTC) {
        clearInterval(checkInterval)
        sdkVersion.value = window.BRTC.VERSION || 'Unknown'
        console.log(`✅ BRTC SDK加载成功，版本: ${sdkVersion.value}`)
        resolve(true)
      } else if (attempts >= maxAttempts) {
        clearInterval(checkInterval)
        console.error('❌ BRTC SDK加载超时')
        resolve(false)
      }
    }, 100)
  })
}

// 初始化BRTC SDK
const initBRTC = async () => {
  try {
    // 等待SDK加载
    const sdkLoaded = await checkSDKLoaded()

    if (sdkLoaded) {
      // 自动初始化BRTC
      await BRTC_Start()
    } else {
      brtcError.value = 'BRTC SDK加载失败'
      showToast('BRTC SDK加载失败')
    }
  } catch (error: any) {
    console.error('BRTC初始化失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  console.log('🚀 语音识别Demo页面已加载')

  // 初始化BRTC SDK
  await initBRTC()
})

onUnmounted(() => {
  console.log('🔄 语音识别Demo页面已卸载')
})
</script>

<template>
  <div class="voice-demo-view">
    <!-- 全屏视频背景 -->
    <div class="video-background">
      <RTMPPlayer
        :rtmp-url="rtmpUrl"
        :autoplay="true"
        :muted="true"
        :controls="false"
        @ready="handleVideoConnected"
        @play="handleVideoPlay"
        @error="handleVideoError"
        class="background-video"
      />
    </div>

    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="status-item">
        <van-icon
          :name="isVideoConnected ? 'success' : 'warning-o'"
          :color="isVideoConnected ? '#07c160' : '#ff976a'"
        />
        <span class="status-text">
          {{ isVideoConnected ? '视频已连接' : '视频连接中...' }}
        </span>
      </div>

      <div class="status-item">
        <van-icon
          :name="brtcInitialized ? 'success' : (brtcError ? 'cross' : 'loading')"
          :color="brtcInitialized ? '#07c160' : (brtcError ? '#ee0a24' : '#ff976a')"
        />
        <span class="status-text">
          {{ brtcInitialized ? 'BRTC已连接' : (brtcError ? 'BRTC连接失败' : 'BRTC连接中...') }}
        </span>
      </div>

      <div class="status-item">
        <van-icon
          :name="isRecording ? 'volume-o' : 'volume'"
          :color="isRecording ? '#ee0a24' : '#969799'"
        />
        <span class="status-text">
          {{ isRecording ? '正在录音' : '待机中' }}
        </span>
      </div>
    </div>

    <!-- 语音识别结果显示 -->
    <div class="recognition-result" v-if="recognizedText">
      <div class="result-content">
        <div class="result-header">
          <span class="result-title">识别结果</span>
          <van-button 
            type="default" 
            size="mini" 
            @click="clearRecognizedText"
            icon="clear"
          >
            清除
          </van-button>
        </div>
        <div class="result-text">{{ recognizedText }}</div>
      </div>
    </div>

    <!-- 底部控制区域 -->
    <div class="control-panel">
      <!-- 语音识别组件 -->
      <BaiduASRRecorder
        @recording-start="handleRecordingStart"
        @recording-stop="handleRecordingStop"
        @recognition-result="handleRecognitionResult"
        @recognition-error="handleRecognitionError"
        class="voice-recorder"
      />
      
    </div>

    <!-- 错误提示 -->
    <div v-if="videoError" class="error-notice">
      <van-notice-bar
        type="warning"
        :text="videoError"
        left-icon="warning-o"
        closeable
        @close="videoError = ''"
      />
    </div>
  </div>
</template>

<style scoped>
.voice-demo-view {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: #000;
}

/* 视频背景 */
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-video {
  width: 100%;
  height: 100%;
}

/* 顶部状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 20px;
  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, transparent 100%);
  z-index: 10;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.status-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}

/* 识别结果显示 */
.recognition-result {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 80%;
  z-index: 10;
}

.result-content {
  background: rgba(0, 0, 0, 0.8);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-title {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.result-text {
  color: #fff;
  font-size: 16px;
  line-height: 1.5;
  min-height: 24px;
  word-break: break-all;
}

/* 底部控制面板 */
.control-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(0deg, rgba(0,0,0,0.8) 0%, transparent 100%);
  z-index: 10;
}

.voice-recorder {
  margin-bottom: 16px;
}

.tips {
  text-align: center;
}

.tip-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 错误提示 */
.error-notice {
  position: absolute;
  top: 80px;
  left: 20px;
  right: 20px;
  z-index: 15;
}

/* 移动端适配 */
@media (max-width: 767px) {
  .status-bar {
    padding: 15px;
  }
  
  .status-item {
    padding: 6px 10px;
  }
  
  .status-text {
    font-size: 11px;
  }
  
  .recognition-result {
    max-width: 90%;
  }
  
  .result-content {
    padding: 16px;
  }
  
  .result-text {
    font-size: 14px;
  }
  
  .control-panel {
    padding: 16px;
  }
  
  .tip-text {
    font-size: 11px;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .status-bar {
    padding: 10px 20px;
  }
  
  .control-panel {
    padding: 10px 20px;
  }
  
  .recognition-result {
    top: 40%;
  }
}
</style>
