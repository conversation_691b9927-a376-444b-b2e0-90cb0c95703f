/**
 * 触摸交互组合式函数
 */
import { ref, onMounted, onUnmounted } from 'vue'

// 触摸反馈
export function useTouchFeedback() {
  const addTouchFeedback = (element: HTMLElement, className = 'touch-active') => {
    const handleTouchStart = () => {
      element.classList.add(className)
    }
    
    const handleTouchEnd = () => {
      setTimeout(() => {
        element.classList.remove(className)
      }, 150)
    }
    
    const handleTouchCancel = () => {
      element.classList.remove(className)
    }
    
    element.addEventListener('touchstart', handleTouchStart, { passive: true })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })
    element.addEventListener('touchcancel', handleTouchCancel, { passive: true })
    
    // 返回清理函数
    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchend', handleTouchEnd)
      element.removeEventListener('touchcancel', handleTouchCancel)
    }
  }
  
  return { addTouchFeedback }
}

// 长按手势
export function useLongPress(callback: () => void, delay = 500) {
  const isPressed = ref(false)
  let timer: NodeJS.Timeout | null = null
  
  const start = () => {
    isPressed.value = true
    timer = setTimeout(() => {
      if (isPressed.value) {
        callback()
      }
    }, delay)
  }
  
  const end = () => {
    isPressed.value = false
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
  }
  
  return {
    isPressed,
    start,
    end
  }
}

// 滑动手势
export function useSwipe() {
  const startX = ref(0)
  const startY = ref(0)
  const endX = ref(0)
  const endY = ref(0)
  const isSwipe = ref(false)
  
  const onTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0]
    startX.value = touch.clientX
    startY.value = touch.clientY
    isSwipe.value = false
  }
  
  const onTouchMove = (e: TouchEvent) => {
    if (!e.touches[0]) return
    
    const touch = e.touches[0]
    endX.value = touch.clientX
    endY.value = touch.clientY
    
    const deltaX = Math.abs(endX.value - startX.value)
    const deltaY = Math.abs(endY.value - startY.value)
    
    // 如果移动距离超过阈值，认为是滑动
    if (deltaX > 10 || deltaY > 10) {
      isSwipe.value = true
    }
  }
  
  const onTouchEnd = () => {
    if (!isSwipe.value) return
    
    const deltaX = endX.value - startX.value
    const deltaY = endY.value - startY.value
    
    // 判断滑动方向
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      // 水平滑动
      if (deltaX > 50) {
        return 'right'
      } else if (deltaX < -50) {
        return 'left'
      }
    } else {
      // 垂直滑动
      if (deltaY > 50) {
        return 'down'
      } else if (deltaY < -50) {
        return 'up'
      }
    }
    
    return null
  }
  
  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd,
    isSwipe
  }
}

// 双击手势
export function useDoubleTap(callback: () => void, delay = 300) {
  let lastTap = 0
  
  const onTap = () => {
    const now = Date.now()
    if (now - lastTap < delay) {
      callback()
    }
    lastTap = now
  }
  
  return { onTap }
}

// 缩放手势
export function usePinch() {
  const scale = ref(1)
  const initialDistance = ref(0)
  
  const getDistance = (touches: TouchList) => {
    const touch1 = touches[0]
    const touch2 = touches[1]
    return Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    )
  }
  
  const onTouchStart = (e: TouchEvent) => {
    if (e.touches.length === 2) {
      initialDistance.value = getDistance(e.touches)
    }
  }
  
  const onTouchMove = (e: TouchEvent) => {
    if (e.touches.length === 2 && initialDistance.value > 0) {
      const currentDistance = getDistance(e.touches)
      scale.value = currentDistance / initialDistance.value
    }
  }
  
  const onTouchEnd = () => {
    initialDistance.value = 0
    scale.value = 1
  }
  
  return {
    scale,
    onTouchStart,
    onTouchMove,
    onTouchEnd
  }
}

// 触摸位置跟踪
export function useTouchPosition() {
  const x = ref(0)
  const y = ref(0)
  const isActive = ref(false)
  
  const onTouchStart = (e: TouchEvent) => {
    isActive.value = true
    const touch = e.touches[0]
    x.value = touch.clientX
    y.value = touch.clientY
  }
  
  const onTouchMove = (e: TouchEvent) => {
    if (!isActive.value) return
    const touch = e.touches[0]
    x.value = touch.clientX
    y.value = touch.clientY
  }
  
  const onTouchEnd = () => {
    isActive.value = false
  }
  
  return {
    x,
    y,
    isActive,
    onTouchStart,
    onTouchMove,
    onTouchEnd
  }
}

// 防抖触摸
export function useDebouncedTouch(callback: () => void, delay = 300) {
  let timer: NodeJS.Timeout | null = null
  
  const debouncedCallback = () => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(callback, delay)
  }
  
  return { debouncedCallback }
}

// 节流触摸
export function useThrottledTouch(callback: () => void, delay = 100) {
  let lastCall = 0
  
  const throttledCallback = () => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      callback()
      lastCall = now
    }
  }
  
  return { throttledCallback }
}
