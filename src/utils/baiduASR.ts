/**
 * 百度ASR语音识别工具类
 * 基于百度语音识别极速版API
 */

// 百度ASR配置接口
export interface BaiduASRConfig {
  appId: string
  apiKey: string
  secretKey: string
  format?: 'pcm' | 'wav' | 'amr' | 'm4a'
  rate?: 16000 | 8000
  channel?: 1
  cuid?: string
  token?: string
}

// 识别结果接口
export interface ASRResult {
  err_no: number
  err_msg: string
  corpus_no?: string
  sn?: string
  result?: string[]
}

// 录音配置
export interface RecordConfig {
  sampleRate: number
  channels: number
  bitsPerSample: number
  maxDuration: number // 最大录音时长（秒）
}

// 回调函数接口
export interface ASRCallbacks {
  onStart?: () => void
  onEnd?: () => void
  onResult?: (text: string) => void
  onError?: (error: string) => void
}

/**
 * 百度ASR管理器
 */
export class BaiduASRManager {
  private config: BaiduASRConfig
  private accessToken: string = ''
  private mediaRecorder: MediaRecorder | null = null
  private audioChunks: Blob[] = []
  private isRecording: boolean = false
  private recordStartTime: number = 0
  private callbacks: ASRCallbacks = {}

  constructor(config: BaiduASRConfig, callbacks: ASRCallbacks = {}) {
    this.config = {
      format: 'pcm',
      rate: 16000,
      channel: 1,
      cuid: this.generateCUID(),
      ...config
    }
    this.callbacks = callbacks
  }

  /**
   * 生成客户端唯一标识
   */
  private generateCUID(): string {
    return 'web_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
  }

  /**
   * 获取访问令牌
   */
  async getAccessToken(): Promise<string> {
    if (this.accessToken) {
      return this.accessToken
    }

    // 开发环境检查
    if (this.isDevMode()) {
      console.warn('开发环境：使用模拟访问令牌')
      this.accessToken = 'dev_mock_token'
      return this.accessToken
    }

    try {
      const response = await fetch('https://aip.baidubce.com/oauth/2.0/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.config.apiKey,
          client_secret: this.config.secretKey
        })
      })

      const data = await response.json()

      if (data.access_token) {
        this.accessToken = data.access_token
        return this.accessToken
      } else {
        throw new Error(`获取访问令牌失败: ${data.error_description || data.error}`)
      }
    } catch (error) {
      console.error('获取百度ASR访问令牌失败:', error)
      throw error
    }
  }

  /**
   * 检查是否为开发模式
   */
  private isDevMode(): boolean {
    return import.meta.env.DEV && (
      this.config.apiKey.includes('placeholder') ||
      this.config.secretKey.includes('placeholder') ||
      this.config.appId.includes('placeholder')
    )
  }

  /**
   * 开始录音
   */
  async startRecording(): Promise<void> {
    try {
      // 请求麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.rate,
          channelCount: this.config.channel,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      // 创建MediaRecorder
      const options: MediaRecorderOptions = {
        mimeType: this.getSupportedMimeType()
      }

      this.mediaRecorder = new MediaRecorder(stream, options)
      this.audioChunks = []
      this.recordStartTime = Date.now()

      // 设置事件监听
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data)
        }
      }

      this.mediaRecorder.onstop = () => {
        this.processRecording()
      }

      // 开始录音
      this.mediaRecorder.start(100) // 每100ms收集一次数据
      this.isRecording = true
      this.callbacks.onStart?.()

      console.log('开始录音...')
    } catch (error) {
      console.error('启动录音失败:', error)
      throw new Error('无法访问麦克风，请检查权限设置')
    }
  }

  /**
   * 停止录音
   */
  stopRecording(): void {
    if (this.mediaRecorder && this.isRecording) {
      this.mediaRecorder.stop()
      this.isRecording = false

      // 停止所有音频轨道
      this.mediaRecorder.stream.getTracks().forEach(track => track.stop())

      this.callbacks.onEnd?.()
      console.log('停止录音')
    }
  }

  /**
   * 获取支持的MIME类型
   */
  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav'
    ]

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type
      }
    }

    return 'audio/webm' // 默认类型
  }

  /**
   * 处理录音数据
   */
  private async processRecording(): Promise<void> {
    try {
      if (this.audioChunks.length === 0) {
        throw new Error('没有录音数据')
      }

      // 合并音频数据
      const audioBlob = new Blob(this.audioChunks, { 
        type: this.getSupportedMimeType() 
      })

      // 检查录音时长
      const duration = (Date.now() - this.recordStartTime) / 1000
      if (duration > 60) {
        throw new Error('录音时长超过60秒限制')
      }

      console.log(`录音完成，时长: ${duration.toFixed(1)}秒，大小: ${audioBlob.size}字节`)

      // 转换音频格式并识别
      await this.recognizeAudio(audioBlob)

    } catch (error) {
      console.error('处理录音失败:', error)
      throw error
    }
  }

  /**
   * 音频识别
   */
  private async recognizeAudio(audioBlob: Blob): Promise<ASRResult> {
    try {
      // 开发环境模拟识别
      if (this.isDevMode()) {
        return this.mockRecognition(audioBlob)
      }

      // 获取访问令牌
      const token = await this.getAccessToken()

      // 转换音频为PCM格式（这里简化处理，实际项目中需要音频格式转换）
      const audioBuffer = await audioBlob.arrayBuffer()
      const base64Audio = this.arrayBufferToBase64(audioBuffer)

      // 构建请求参数
      const requestData = {
        format: this.config.format,
        rate: this.config.rate,
        channel: this.config.channel,
        cuid: this.config.cuid,
        token: token,
        speech: base64Audio,
        len: audioBuffer.byteLength
      }

      // 发送识别请求
      const response = await fetch('https://vop.baidu.com/pro_api', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      const result: ASRResult = await response.json()

      if (result.err_no === 0 && result.result) {
        const recognizedText = result.result[0]
        console.log('识别成功:', recognizedText)
        this.callbacks.onResult?.(recognizedText)
        return result
      } else {
        const errorMsg = `识别失败: ${result.err_msg} (错误码: ${result.err_no})`
        this.callbacks.onError?.(errorMsg)
        throw new Error(errorMsg)
      }

    } catch (error) {
      console.error('语音识别失败:', error)
      const errorMsg = error instanceof Error ? error.message : '语音识别失败'
      this.callbacks.onError?.(errorMsg)
      throw error
    }
  }

  /**
   * 开发环境模拟识别
   */
  private async mockRecognition(audioBlob: Blob): Promise<ASRResult> {
    console.warn('开发环境：使用模拟语音识别')

    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

    // 模拟识别结果
    const mockResults = [
      '你好，这是一个语音识别测试',
      '欢迎使用百度ASR语音识别服务',
      '这是开发环境的模拟识别结果',
      '请配置真实的API密钥以使用正式服务',
      '语音识别功能正常工作'
    ]

    const randomResult = mockResults[Math.floor(Math.random() * mockResults.length)]

    const result: ASRResult = {
      err_no: 0,
      err_msg: 'success',
      result: [randomResult]
    }

    console.log('模拟识别成功:', randomResult)
    this.callbacks.onResult?.(randomResult)

    return result
  }

  /**
   * ArrayBuffer转Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  /**
   * 检查录音状态
   */
  isCurrentlyRecording(): boolean {
    return this.isRecording
  }

  /**
   * 获取录音时长
   */
  getRecordingDuration(): number {
    if (!this.isRecording) return 0
    return (Date.now() - this.recordStartTime) / 1000
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    if (this.isRecording) {
      this.stopRecording()
    }
    this.mediaRecorder = null
    this.audioChunks = []
    this.accessToken = ''
  }
}

/**
 * 创建百度ASR实例的便捷函数
 */
export function createBaiduASR(config: BaiduASRConfig, callbacks: ASRCallbacks = {}): BaiduASRManager {
  return new BaiduASRManager(config, callbacks)
}

/**
 * 检查浏览器是否支持录音
 */
export function checkRecordingSupport(): {
  supported: boolean
  mediaRecorder: boolean
  getUserMedia: boolean
  message: string
} {
  const mediaRecorderSupported = typeof MediaRecorder !== 'undefined'
  const getUserMediaSupported = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
  const supported = mediaRecorderSupported && getUserMediaSupported

  let message = ''
  if (!mediaRecorderSupported) {
    message = '浏览器不支持MediaRecorder API'
  } else if (!getUserMediaSupported) {
    message = '浏览器不支持getUserMedia API'
  } else {
    message = '浏览器支持录音功能'
  }

  return {
    supported,
    mediaRecorder: mediaRecorderSupported,
    getUserMedia: getUserMediaSupported,
    message
  }
}

/**
 * 百度ASR错误码说明
 */
export const BAIDU_ASR_ERROR_CODES: Record<number, string> = {
  0: '识别成功',
  3300: '输入参数不正确',
  3301: '音频质量过差',
  3302: '鉴权失败',
  3303: '语音服务器后端问题',
  3304: '用户的请求QPS超限',
  3305: '用户的日pv（日请求量）超限',
  3307: '语音过长',
  3308: '音频无效',
  3309: '音频文件过大',
  3310: '音频文件base64解码失败',
  3311: '音频文件参数不匹配',
  3312: '音频文件格式不支持'
}

/**
 * 获取错误信息
 */
export function getASRErrorMessage(errorCode: number): string {
  return BAIDU_ASR_ERROR_CODES[errorCode] || `未知错误 (${errorCode})`
}
