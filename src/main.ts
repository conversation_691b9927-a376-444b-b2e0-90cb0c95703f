import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
// Vant 样式
import 'vant/lib/index.css'
import './assets/main.scss'
import './styles/mobile.scss'
import './styles/vant-theme.scss'
import './styles/scrollbar.css'
import { initMobileAdaptation } from './utils/mobile'

// 初始化移动端适配
initMobileAdaptation()


// 全局BRTC对象声明
declare global {
  interface Window {
    BRTC?: any
  }
}

// 创建模拟的BRTC SDK对象
function createMockBRTC() {
  return {
    VERSION: '0.7.69-mock',

    /**
     * BRTC_Start() - 启动BRTC SDK
     * @param {string} server - 百度的RTC服务器
     * @param {string} appid - 百度派发的AppID
     * @param {string} token - app server派发的token字符串
     */
    Start: function(server: string, appid: string, token: string) {
      return new Promise((resolve, reject) => {
        // 参数验证
        if (!appid || typeof appid !== 'string') {
          const error = new Error('AppID不能为空且必须是字符串');
          console.error('❌ BRTC初始化失败:', error.message);
          reject(error);
          return;
        }

        if (!token || typeof token !== 'string') {
          const error = new Error('Token不能为空且必须是字符串');
          console.error('❌ BRTC初始化失败:', error.message);
          reject(error);
          return;
        }

        // 模拟异步初始化过程
        setTimeout(() => {
          try {
            const result = {
              success: true,
              message: 'BRTC SDK初始化成功',
              server: server || 'wss://rtc.baidubce.com',
              appid: appid,
              timestamp: Date.now(),
              sdkVersion: this.VERSION
            };

            console.log('✅ BRTC SDK初始化成功:', result);
            resolve(result);

          } catch (error) {
            console.error('❌ BRTC初始化过程中出错:', error);
            reject(error);
          }
        }, 800);
      });
    },

    createClient: function(config = {}) {
      console.log('📱 BRTC.createClient() 被调用:', config);
      return {
        id: 'brtc-client-' + Date.now(),
        config: config
      };
    },

    LOG_LEVEL: {
      DEBUG: 0,
      INFO: 1,
      WARNING: 2,
      ERROR: 3,
      NONE: 4
    },

    setLogLevel: function(level: number) {
      console.log('📊 BRTC.setLogLevel() 被调用:', level);
    }
  };
}

async function loadBRTC() {
  try {
    console.log('🔄 开始加载BRTC SDK...');

    // 从 public 目录加载 SDK
    const script = document.createElement('script');
    script.src = '/baidu/baidu.rtc.sdk.js';
    script.async = true;

    return new Promise((resolve, reject) => {
      script.onload = () => {
        console.log('📦 SDK文件加载完成');

        // 检查是否有真正的BRTC对象
        if (window.BRTC) {
          console.log('✅ 发现真正的BRTC SDK:', window.BRTC);
          resolve(window.BRTC);
        } else {
          console.log('⚠️ 未发现BRTC对象，使用模拟SDK');
          // 创建并设置模拟的BRTC对象
          window.BRTC = createMockBRTC();
          resolve(window.BRTC);
        }
      };

      script.onerror = () => {
        console.error('❌ SDK文件加载失败，使用模拟SDK');
        // 即使加载失败也提供模拟SDK
        window.BRTC = createMockBRTC();
        resolve(window.BRTC);
      };

      document.head.appendChild(script);
    });
  } catch (error) {
    console.error('❌ 加载 BRTC SDK 时出错:', error);
    // 提供模拟SDK作为后备
    window.BRTC = createMockBRTC();
    return window.BRTC;
  }
}

// 初始化应用
async function initApp() {
  try {
    // 加载BRTC SDK
    await loadBRTC()
    console.log('✅ BRTC SDK 已准备就绪')
  } catch (error) {
    console.error('❌ BRTC SDK 初始化失败:', error)
  }

  // 创建Vue应用
  const app = createApp(App)
  app.use(router)
  app.mount('#app')
}

// 启动应用
initApp()