import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'
import HomeView from '../views/HomeView/HomeView.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'home',
    component: HomeView,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/voice-demo',
    name: 'voiceDemo',
    component: () => import('../views/VoiceDemoView/VoiceDemoView.vue'),
    meta: {
      title: '语音识别Demo',
      requiresPermissions: ['microphone']
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('../views/NotFoundView.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫示例
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 数字生命4.0`
  }

  // 检查权限要求
  if (to.meta.requiresPermissions) {
    const permissions = to.meta.requiresPermissions as string[]

    // 检查麦克风权限（针对语音识别页面）
    if (permissions.includes('microphone')) {
      // 在移动端，我们在组件内部处理权限请求
      // 这里只是记录访问日志
      console.log('访问需要麦克风权限的页面:', to.path)
    }
  }

  next()
})

export default router
