/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BaiduRTMPPlayer: typeof import('./src/components/BaiduRTMPPlayer.vue')['default']
    BRTCTest: typeof import('./src/components/BRTCTest.vue')['default']
    DirectRTMPPlayer: typeof import('./src/components/DirectRTMPPlayer.vue')['default']
    EnhancedRTMPPlayer: typeof import('./src/components/EnhancedRTMPPlayer.vue')['default']
    ExampleCard: typeof import('./src/components/ExampleCard.vue')['default']
    InfiniteScroll: typeof import('./src/components/InfiniteScroll.vue')['default']
    LazyImage: typeof import('./src/components/LazyImage.vue')['default']
    OptimizedRTMPPlayer: typeof import('./src/components/OptimizedRTMPPlayer.vue')['default']
    PerformanceMonitor: typeof import('./src/components/PerformanceMonitor.vue')['default']
    PullRefresh: typeof import('./src/components/PullRefresh.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RTMPPlayer: typeof import('./src/components/RTMPPlayer.vue')['default']
    SimpleRTMPPlayer: typeof import('./src/components/SimpleRTMPPlayer.vue')['default']
    SimpleRTMPVideoPlayer: typeof import('./src/components/SimpleRTMPVideoPlayer.vue')['default']
    StaticVideoPlayer: typeof import('./src/components/StaticVideoPlayer.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCard: typeof import('vant/es')['Card']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCollapse: typeof import('vant/es')['Collapse']
    VanCollapseItem: typeof import('vant/es')['CollapseItem']
    VanDivider: typeof import('vant/es')['Divider']
    VanField: typeof import('vant/es')['Field']
    VanGrid: typeof import('vant/es')['Grid']
    VanGridItem: typeof import('vant/es')['GridItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanNoticeBar: typeof import('vant/es')['NoticeBar']
    VanPopup: typeof import('vant/es')['Popup']
    VanProgress: typeof import('vant/es')['Progress']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanSearch: typeof import('vant/es')['Search']
    VanSwipeCell: typeof import('vant/es')['SwipeCell']
    VanTag: typeof import('vant/es')['Tag']
    VLCPlayer: typeof import('./src/components/VLCPlayer.vue')['default']
    VueVideoRTMPPlayer: typeof import('./src/components/VueVideoRTMPPlayer.vue')['default']
    WebRTCPlayer: typeof import('./src/components/WebRTCPlayer.vue')['default']
  }
}
