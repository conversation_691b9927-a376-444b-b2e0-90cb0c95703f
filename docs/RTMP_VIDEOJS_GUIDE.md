# 🎥 Video.js RTMP播放器使用指南

## 📋 概述

根据您的需求，我已经实现了使用Video.js + Flash技术来直接播放RTMP流的解决方案。

**RTMP地址**: `rtmp://ns8.indexforce.com/home/<USER>

## ✅ 已完成的工作

### 1. 依赖安装
```bash
npm install videojs-flash videojs-contrib-hls -S
```

### 2. 全局配置 (main.ts)
```typescript
// 引入 Video.js 和相关插件
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import 'videojs-flash'
import 'videojs-contrib-hls'

// 将videojs挂载到全局属性上
app.config.globalProperties.$video = videojs
```

### 3. RTMPPlayer组件
- ✅ 支持RTMP流直接播放
- ✅ 使用Video.js + Flash技术
- ✅ 自动检测流类型
- ✅ 多重播放策略回退
- ✅ 完整的事件处理

### 4. Demo集成
- ✅ 已集成到语音识别Demo
- ✅ 全屏背景播放
- ✅ 响应式设计

## 🧪 测试方法

### 1. 专门的Video.js测试页面
```
http://localhost:5173/test-rtmp-videojs.html
```

**功能**:
- 检查Flash支持
- 初始化Video.js播放器
- 直接播放RTMP流
- 详细的日志输出

### 2. 完整的语音识别Demo
```
http://localhost:5173/#/voice-demo
```

**功能**:
- RTMP视频背景
- 语音识别功能
- 完整的用户体验

## ⚠️ Flash支持说明

### 现代浏览器Flash设置

由于现代浏览器默认禁用Flash，需要手动启用：

#### Chrome浏览器
1. 地址栏输入: `chrome://settings/content/flash`
2. 将目标网站添加到"允许"列表
3. 或者在地址栏点击"锁"图标 → Flash → 允许

#### Firefox浏览器
1. 访问: `about:addons`
2. 找到"Shockwave Flash"插件
3. 设置为"总是激活"

#### Edge浏览器
1. 设置 → 高级设置
2. 启用"使用Adobe Flash Player"

### Flash检测代码
```javascript
// 检查Flash支持
const hasFlash = navigator.plugins && navigator.plugins['Shockwave Flash'];
console.log('Flash支持:', hasFlash);
```

## 🔧 技术实现

### Video.js配置
```javascript
const player = videojs('video-element', {
  controls: true,
  autoplay: false,
  muted: true,
  techOrder: ['flash', 'html5'], // 优先使用Flash
  flash: {
    swf: 'https://vjs.zencdn.net/swf/5.4.2/video-js.swf'
  }
});

// 设置RTMP源
player.src({
  src: 'rtmp://ns8.indexforce.com/home/<USER>',
  type: 'rtmp/flv'
});
```

### RTMPPlayer组件使用
```vue
<template>
  <RTMPPlayer
    :url="rtmpUrl"
    :autoplay="true"
    :muted="true"
    :controls="false"
    :fluid="true"
    @ready="handleReady"
    @play="handlePlay"
    @error="handleError"
  />
</template>

<script setup>
import RTMPPlayer from '@/components/RTMPPlayer.vue'

const rtmpUrl = 'rtmp://ns8.indexforce.com/home/<USER>'

const handleReady = () => {
  console.log('播放器准备就绪')
}

const handlePlay = () => {
  console.log('开始播放')
}

const handleError = (error) => {
  console.error('播放错误:', error)
}
</script>
```

## 🎯 播放策略

RTMPPlayer组件使用智能播放策略：

1. **RTMP流**: 优先使用Video.js + Flash
2. **回退方案**: 如果Flash不可用，尝试转换为FLV/HLS
3. **FLV流**: 使用flv.js
4. **HLS流**: 使用hls.js
5. **其他格式**: 使用原生HTML5播放器

## 🚀 立即测试

### 步骤1: 启动开发服务器
```bash
npm run dev
```

### 步骤2: 测试Flash支持
访问: `http://localhost:5173/test-rtmp-videojs.html`
1. 点击"检查Flash支持"
2. 根据提示启用Flash
3. 点击"初始化播放器"
4. 点击"播放RTMP"

### 步骤3: 测试完整Demo
访问: `http://localhost:5173/#/voice-demo`

## 💡 故障排除

### 问题1: Flash未启用
**解决方案**: 按照上述浏览器设置启用Flash

### 问题2: SWF文件加载失败
**解决方案**: 
- 检查网络连接
- 使用本地SWF文件
- 确保CDN可访问

### 问题3: RTMP连接失败
**解决方案**:
- 检查RTMP URL是否正确
- 确认服务器是否在线
- 查看浏览器控制台错误信息

### 问题4: 自动播放被阻止
**解决方案**:
- 设置`muted: true`
- 用户手动点击播放
- 检查浏览器自动播放策略

## 🎉 预期效果

如果一切正常，您应该能看到：

1. ✅ **Flash检测通过**: 浏览器支持Flash
2. ✅ **播放器初始化成功**: Video.js加载完成
3. ✅ **RTMP流连接成功**: 开始播放视频
4. ✅ **视频显示正常**: 可以看到数字人视频

## 📞 技术支持

如果遇到问题：

1. **检查浏览器控制台**: 查看详细错误信息
2. **测试其他浏览器**: 确认是否为浏览器特定问题
3. **检查网络连接**: 确保可以访问RTMP服务器
4. **联系百度技术支持**: 确认RTMP流是否正常

---

**现在就开始测试吧！** 🚀
