# 语音实时识别Demo应用

## 概述

这是一个基于Vue3和Vant UI的移动端语音实时识别demo应用，使用百度ASR语音识别极速版API，集成了数字人视频流播放和实时语音识别功能。

## 功能特性

### 🎯 核心功能
- **百度ASR语音识别**: 使用百度语音识别极速版API，支持60秒内音频识别
- **RTMP视频流播放**: 支持数字人视频流播放，视频作为全屏背景
- **极简UI设计**: 视频背景+底部字幕式语音识别结果显示
- **移动端优化**: 完全适配移动设备触摸交互

### 🛠 技术特性
- **专业音频处理**: 支持PCM格式录音和音频格式转换
- **权限管理**: 智能处理麦克风权限请求
- **错误处理**: 完善的错误提示和重试机制
- **配置管理**: 支持环境变量配置API密钥

## 项目结构

```
src/views/VoiceDemoView/
├── VoiceDemoView.vue              # 主页面组件（极简设计）
├── components/
│   ├── RTMPVideoPlayer.vue        # RTMP视频播放组件
│   └── BaiduASRRecorder.vue       # 百度ASR录音组件
src/utils/
├── baiduASR.ts                    # 百度ASR语音识别工具类
├── audioProcessor.ts             # 音频处理工具类
└── rtmpPlayer.ts                  # 流媒体播放工具类
src/config/
└── baiduASR.ts                    # 百度ASR配置文件
```

## 快速开始

### 1. 配置百度ASR API
1. 访问 [百度AI开放平台](https://ai.baidu.com) 注册账号
2. 创建应用并开通"语音技术"中的"短语音识别极速版"服务
3. 获取AppID、API Key、Secret Key
4. 复制 `.env.example` 为 `.env.local`
5. 在 `.env.local` 中填入您的API密钥：
   ```
   VITE_BAIDU_APP_ID=你的AppID
   VITE_BAIDU_API_KEY=你的API_Key
   VITE_BAIDU_SECRET_KEY=你的Secret_Key
   ```

### 2. 启动应用
```bash
npm install
npm run dev
```

### 3. 使用Demo
1. 在首页点击"语音Demo"按钮进入页面
2. 点击底部录音按钮开始语音识别
3. 识别结果会以字幕形式显示在底部
4. 数字人视频作为全屏背景播放

## 技术实现

### 百度ASR语音识别
- 使用百度语音识别极速版API
- 支持60秒内完整音频识别
- 专有GPU服务集群，识别速度快，准确率高

```typescript
// 百度ASR配置
const config = {
  format: 'pcm',          // 音频格式
  rate: 16000,            // 采样率
  channel: 1,             // 单声道
  maxDuration: 60         // 最大60秒
}
```

### 视频流播放
- 支持RTMP流自动转换为HLS格式
- 移动端播放优化（内联播放、全屏支持）
- 自动重试和错误恢复机制

```typescript
// 流媒体配置
const streamConfig = {
  url: 'rtmp://example.com/live/stream',
  autoplay: true,
  muted: true,
  controls: false
}
```

## 浏览器支持

### 录音功能支持
- ✅ Chrome 47+
- ✅ Firefox 29+
- ✅ Safari 14.1+
- ✅ Edge 79+

### 流媒体支持
- ✅ HLS: Safari, Chrome (with MSE)
- ✅ DASH: Chrome, Firefox, Edge
- ✅ WebRTC: 所有现代浏览器

### 音频格式支持
- ✅ PCM (推荐)
- ✅ WAV
- ✅ AMR
- ✅ M4A

## 移动端优化

### 触摸交互
- 44px最小触摸目标尺寸
- 触摸反馈动画效果
- 防止意外触发的延迟处理

### 屏幕适配
- 安全区域适配（刘海屏支持）
- 横竖屏自动适配
- 响应式字体和间距

### 性能优化
- 懒加载和按需引入
- 内存泄漏防护
- 电池优化（自动暂停等）

## 配置说明

### 环境要求
- **HTTPS**: 语音识别需要安全上下文
- **麦克风权限**: 用户需要授权麦克风使用
- **网络连接**: 流媒体播放需要稳定网络

### 自定义配置

#### 修改语音识别语言
```typescript
// 在 VoiceRecognition.vue 中修改
const props = withDefaults(defineProps<Props>(), {
  lang: 'en-US', // 改为英文识别
  // ... 其他配置
})
```

#### 修改视频流地址
```typescript
// 在 VoiceDemoView.vue 中修改
const initVideoUrl = () => {
  videoUrl.value = 'your-rtmp-url-here'
}
```

## 故障排除

### 常见问题

1. **语音识别不工作**
   - 检查是否为HTTPS环境
   - 确认麦克风权限已授权
   - 检查浏览器是否支持Web Speech API

2. **视频无法播放**
   - 检查RTMP流地址是否正确
   - 确认网络连接稳定
   - 尝试刷新页面重新加载

3. **移动端显示异常**
   - 检查viewport设置
   - 确认CSS媒体查询生效
   - 测试不同设备和浏览器

### 调试方法
```javascript
// 开启调试日志
localStorage.setItem('debug', 'voice-demo:*')

// 检查语音识别支持
import { checkSpeechRecognitionAvailability } from '@/utils/speechRecognition'
console.log(checkSpeechRecognitionAvailability())

// 检查流媒体支持
import { checkStreamingCapability } from '@/utils/rtmpPlayer'
console.log(checkStreamingCapability())
```

## 扩展开发

### 添加新的语音命令
```typescript
const handleVoiceResult = (text: string) => {
  // 添加语音命令处理
  if (text.includes('播放')) {
    // 执行播放操作
  } else if (text.includes('暂停')) {
    // 执行暂停操作
  }
  // ... 更多命令
}
```

### 集成其他流媒体服务
```typescript
// 在 rtmpPlayer.ts 中添加新的转换器
export class CustomStreamConverter {
  static convertToCustomFormat(url: string): string {
    // 自定义转换逻辑
    return convertedUrl
  }
}
```

## 许可证

本项目遵循 MIT 许可证。详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>
